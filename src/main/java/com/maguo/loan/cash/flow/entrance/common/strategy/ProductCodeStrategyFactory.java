package com.maguo.loan.cash.flow.entrance.common.strategy;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 产品代码策略的统一工厂。
 * 负责根据来源（source）查找并执行相应的顶层策略
 */
@Component
public class ProductCodeStrategyFactory {

    // 持有所有顶层策略的列表
    private final List<ProductCodeStrategy> strategies;

    public ProductCodeStrategyFactory(List<ProductCodeStrategy> strategies) {
        this.strategies = strategies;
    }


    /**
     * @param params 构建 productCode 所需的参数
     * @return 构建好的 productCode
     */
    public String getProductCode(String source, Map<String, String> params) {
        Optional<ProductCodeStrategy> strategyOpt = strategies.stream()
            // 使用新的 supports 签名进行过滤
            .filter(s -> s.supports(source, params))
            .findFirst();

        if (strategyOpt.isPresent()) {
            return strategyOpt.get().buildProductCode(params);
        }

        throw new RuntimeException("未能找到支持的策略，source: " + source + ", params: " + params);
    }
}

