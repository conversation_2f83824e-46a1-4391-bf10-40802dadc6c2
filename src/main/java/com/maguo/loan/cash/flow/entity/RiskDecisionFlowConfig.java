package com.maguo.loan.cash.flow.entity;

import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.ApplyType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

@Entity
@Table(name = "risk_decision_flow_config")
public class RiskDecisionFlowConfig extends BaseEntity{
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    private String applyChannel;
    /**
     * 资金方
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "bank_channel")
    private BankChannel bankChannel;


    //决策流编号
    private String decisionFlowNo;

    //密钥
    private String secretKey;

    //阶段
    @Enumerated(EnumType.STRING)
    private ApplyType stage;

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(String applyChannel) {
        this.applyChannel = applyChannel;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getDecisionFlowNo() {
        return decisionFlowNo;
    }

    public void setDecisionFlowNo(String decisionFlowNo) {
        this.decisionFlowNo = decisionFlowNo;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public ApplyType getStage() {
        return stage;
    }

    public void setStage(ApplyType stage) {
        this.stage = stage;
    }
}
